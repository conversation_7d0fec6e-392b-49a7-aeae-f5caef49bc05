/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* CSS Variables for consistent theming */
:root {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --border-color: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Utility classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.card {
  background: var(--surface-color);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Grid system */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* Responsive design */
@media (max-width: 768px) {
  .grid-cols-2,
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 0 0.5rem;
  }
  
  .card {
    padding: 1rem;
  }
}

/* Loading and animations */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status indicators */
.status-income {
  color: var(--success-color);
}

.status-expense {
  color: var(--danger-color);
}

.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.badge-success {
  background-color: #dcfce7;
  color: #166534;
}

.badge-danger {
  background-color: #fee2e2;
  color: #991b1b;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

/* Dashboard specific styles */
.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-subtitle {
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.summary-card {
  border-left: 4px solid var(--primary-color);
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.summary-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
}

.summary-icon.income {
  background-color: #dcfce7;
  color: var(--success-color);
}

.summary-icon.expense {
  background-color: #fee2e2;
  color: var(--danger-color);
}

.summary-info h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.summary-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.summary-amount.positive {
  color: var(--success-color);
}

.summary-amount.negative {
  color: var(--danger-color);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.quick-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.empty-state p {
  margin-bottom: 1rem;
}

/* Page header styles */
.page-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* Form styles */
.transaction-form {
  max-width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
  z-index: 1;
}

.input-group .form-input {
  padding-left: 2rem;
}

.form-input.error,
.form-select.error {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger-color);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

textarea.form-input {
  resize: vertical;
  min-height: 80px;
}

/* Chart styles */
.chart-tooltip {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.75rem;
  box-shadow: var(--shadow);
}

.tooltip-label {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.tooltip-value {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.chart-legend {
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  flex: 1;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.legend-amount {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.chart-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--text-secondary);
}

/* Transaction styles */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.transaction-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-indicator {
  width: 4px;
  height: 24px;
  border-radius: 2px;
}

.transaction-details {
  flex: 1;
}

.transaction-description {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.transaction-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.transaction-amount {
  font-weight: 600;
  font-size: 1rem;
}

.transaction-amount.income {
  color: var(--success-color);
}

.transaction-amount.expense {
  color: var(--danger-color);
}

.transactions-footer {
  margin-top: 1.5rem;
  text-align: center;
}

.empty-icon {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .transaction-item {
    padding: 0.75rem;
  }

  .transaction-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Transaction History styles */
.summary-mini {
  padding: 1rem;
}

.summary-mini-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-mini-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.summary-mini-value {
  font-size: 1.25rem;
  font-weight: 700;
}

.summary-mini-value.income {
  color: var(--success-color);
}

.summary-mini-value.expense {
  color: var(--danger-color);
}

.filters-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-box {
  position: relative;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.transactions-table {
  display: flex;
  flex-direction: column;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  align-items: center;
}

.table-row:hover {
  background: #f8fafc;
}

.transaction-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.transaction-notes {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.transaction-category {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.transaction-date {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.transaction-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background: var(--secondary-color);
  color: white;
}

.btn-icon.btn-danger:hover {
  background: var(--danger-color);
  color: white;
}

@media (max-width: 768px) {
  .table-header {
    display: none;
  }

  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;
  }

  .transaction-info {
    order: 1;
  }

  .transaction-category {
    order: 2;
  }

  .transaction-date {
    order: 3;
  }

  .transaction-amount {
    order: 4;
    font-size: 1.125rem;
  }

  .transaction-actions {
    order: 5;
    justify-content: flex-end;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }
}

/* Categories styles */
.category-form {
  max-width: 100%;
}

.color-picker {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 0.5rem;
  max-width: 400px;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: var(--text-primary);
  transform: scale(1.1);
  box-shadow: 0 0 0 2px var(--surface-color), 0 0 0 4px var(--primary-color);
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.category-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-color {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.category-name {
  font-weight: 500;
  color: var(--text-primary);
}

.category-actions {
  display: flex;
  gap: 0.5rem;
}

/* Settings styles */
.settings-form {
  max-width: 100%;
}

.data-actions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.action-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: #f8fafc;
}

.action-item.danger {
  border-color: #fecaca;
  background: #fef2f2;
}

.action-info h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.action-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.app-info {
  color: var(--text-secondary);
  line-height: 1.6;
}

.app-info p {
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .action-item {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .color-picker {
    grid-template-columns: repeat(auto-fill, minmax(35px, 1fr));
  }

  .color-option {
    width: 35px;
    height: 35px;
  }
}
